<?php

namespace App\Http\Controllers\Admin;

use Exception;
use App\Helpers\FileHelper;
use App\Models\Testimonial;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Storage;
use Yajra\DataTables\Facades\DataTables;

class TestimonialController extends Controller
{
    public function index(Request $request)
    {
        try {
            if ($request->ajax()) {
                $testimonials = Testimonial::select('testimonials_id', 'name', 'title', 'image', 'status')
                    ->orderBy('created_at', 'desc')
                    ->get();

                return DataTables::of($testimonials)
                    ->addIndexColumn()

                    ->editColumn('name', fn($t) => ucwords(strtolower($t->name ?? '')))
                    ->editColumn('title', fn($t) => ucwords($t->title ?? '-'))

                    ->editColumn('image', function ($t) {
                        $img = $t->image ? asset($t->image) : asset('assets/images/default.jpg');
                        return '<img src="' . $img . '" alt="Image" width="50" height="50" class="rounded" >';
                    })

                    ->editColumn('status', function ($t) {
                        $status = $t->status ? 'Active' : 'Inactive';
                        $btnClass = $t->status ? 'btn-success' : 'btn-secondary';

                        return '<button type="button" class="m-0 btn btn-sm toggle-status ' . $btnClass . '" data-id="' . $t->testimonials_id . '">' . $status . '</button>';
                    })

                    ->editColumn('created_at', fn($t) => optional($t->created_at)->format('d M, Y h:i A'))

                    ->addColumn('actions', function ($t) {
                        $id = $t->testimonials_id;
                        $viewRoute = route('testimonial.show', $id);
                        $editRoute = route('testimonial.edit', $id);

                        return '
                        <div class="d-flex gap-2">
                            <a href="' . $viewRoute . '" class="text-info ttt" title="View">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="' . $editRoute . '" class="text-primary ttt" title="Edit">
                                <i class="fas fa-edit"></i>
                            </a>
                            <button type="button" class="text-danger border-0 bg-transparent p-0 m-0 delete-button ttt" data-id="' . $id . '" title="Delete">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>';
                    })

                    ->rawColumns(['image', 'status', 'actions'])
                    ->make(true);
            }

            return view('admin.testimonial.index');
        } catch (\Exception $e) {
            Log::error('Error fetching testimonial data: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Something went wrong while loading the data.');
        }
    }

    public function create()
    {
        return view('admin.testimonial.create');
    }

    public function store(Request $request)
    {
        try {
            $validated = $request->validate([
                'name'        => 'required|string|max:25|min:3',
                'title'       => 'required|string|max:30',
                'description' => 'required|string|max:1000',
                'image'       => 'required|image|mimes:jpg,jpeg,png,webp|max:5120',
                'status'      => 'required|in:0,1',
            ]);

            $testimonial = new Testimonial([
                'testimonials_id' => (string) Str::uuid(),
                'name'            => $validated['name'],
                'title'           => $validated['title'],
                'description'     => $validated['description'],
                'status'          => $validated['status'] ?? 1,
            ]);

            if ($request->hasFile('image')) {
                $file = $request->file('image');
                $uploadedPath = FileHelper::uploadFiles($file, 'testimonials', 'public');
                $testimonial->image = $uploadedPath;
            }

            $testimonial->save();

            return redirect()->route('testimonial.index')->with('success', 'Testimonial created successfully.');
        } catch (Exception $e) {
            Log::error('Failed to store testimonial', ['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);

            return redirect()->back()->with('error', 'Something went wrong. Please try again.');
        }
    }

    public function show(string $id)
    {
        $testimonial = Testimonial::where('testimonials_id', $id)->firstOrFail();
        return view('admin.testimonial.show', compact('testimonial'));
    }

    public function edit(string $id)
    {
        $testimonial = Testimonial::where('testimonials_id', $id)->firstOrFail();
        return view('admin.testimonial.edit', compact('testimonial'));
    }

    public function update(Request $request, string $id)
    {
        try {
            $testimonial = Testimonial::where('testimonials_id', $id)->firstOrFail();

            $validated = $request->validate([
                'name'        => 'required|string|max:25|min:3',
                'title'       => 'required|string|max:30',
                'description' => 'required|string|max:1000',
                'image'       => 'nullable|image|mimes:jpg,jpeg,png,webp|max:5120',
                'status'      => 'required|in:0,1',
            ]);

            $testimonial->name        = $validated['name'];
            $testimonial->title       = $validated['title'];
            $testimonial->description = $validated['description'];
            $testimonial->status      = $validated['status'];

            if ($request->hasFile('image')) {
                // Delete old image if exists
                if ($testimonial->image && Storage::disk('public')->exists($testimonial->image)) {
                    Storage::disk('public')->delete($testimonial->image);
                }

                // Upload new image using helper
                $file = $request->file('image');
                $uploadedPath = FileHelper::uploadFiles($file, 'testimonials', 'public');
                $testimonial->image = $uploadedPath;
            }

            $testimonial->save();

            return redirect()->route('testimonial.index')->with('success', 'Testimonial updated successfully.');
        } catch (Exception $e) {
            Log::error('Failed to update testimonial', ['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);

            return redirect()->back()->with('error', 'Something went wrong. Please try again.');
        }
    }

    public function destroy($id)
    {
        try {
            $testimonial = Testimonial::where('testimonials_id', $id)->firstOrFail();

            if ($testimonial->image) {
                $storagePath = str_replace('storage/', '', $testimonial->image);

                FileHelper::deleteFile($storagePath, 'public');
            }

            $testimonial->delete();

            return response()->json(['status' => true, 'message' => 'Testimonial deleted successfully.']);
        } catch (Exception $e) {
            return response()->json(['status' => false, 'message' => 'Something went wrong.'], 500);
        }
    }

    public function status($id)
    {
        try {
            $testimonial = Testimonial::findOrFail($id);
            $testimonial->status = !$testimonial->status;
            $testimonial->save();

            return response()->json([
                'status' => true,
                'newStatus' => $testimonial->status ? 'Active' : 'Inactive'
            ]);
        } catch (\Exception $e) {
            Log::error('Status toggle failed: ' . $e->getMessage());
            return response()->json(['status' => false], 500);
        }
    }
}
