@extends('layouts.app')

@section('content')
    @include('layouts.navbars.auth.topnav', ['title' => 'Contact Messages'])

    <div class="container-fluid py-4">
        <div class="row justify-content-center">
            <div class="col-lg-12 col-md-12">
                <div class="card mb-4">
                    <div class="d-flex justify-content-between px-3 py-4">
                        <h6 class="mb-0" style="color: #67748e;">Contact Messages</h6>
                        <div class="d-flex gap-2">
                            <span class="badge badge-warning">
                                <i class="fas fa-clock"></i> Pending: <span id="pending-count">0</span>
                            </span>
                            <span class="badge badge-success">
                                <i class="fas fa-eye"></i> Viewed: <span id="viewed-count">0</span>
                            </span>
                        </div>
                    </div>

                    <div class="card-body p-3">
                        <x-data-table id="contactsTable" :ajax="route('contact.index')" :columns="[
                            [
                                'data' => 'DT_RowIndex',
                                'name' => 'DT_RowIndex',
                                'orderable' => false,
                                'searchable' => false,
                            ],
                            ['data' => 'full_name', 'name' => 'full_name'],
                            ['data' => 'email', 'name' => 'email'],
                            ['data' => 'phone', 'name' => 'phone'],
                            ['data' => 'subject', 'name' => 'subject'],
                            ['data' => 'status', 'name' => 'status'],
                            ['data' => 'created_at', 'name' => 'created_at'],
                            ['data' => 'actions', 'name' => 'actions', 'orderable' => false, 'searchable' => false],
                        ]" :order="[]">
                            <x-slot:header>
                                <th>S.No.</th>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Phone</th>
                                <th>Subject</th>
                                <th>Status</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </x-slot:header>
                        </x-data-table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>
        $(document).ready(function() {
            const table = $('#contactsTable').DataTable();

            // Update counts after table loads
            table.on('draw', function() {
                updateStatusCounts();
            });

            function updateStatusCounts() {
                let pendingCount = 0;
                let viewedCount = 0;
                
                table.rows().every(function() {
                    const data = this.data();
                    if (data.status && data.status.includes('Pending')) {
                        pendingCount++;
                    } else if (data.status && data.status.includes('Viewed')) {
                        viewedCount++;
                    }
                });
                
                $('#pending-count').text(pendingCount);
                $('#viewed-count').text(viewedCount);
            }

            // Status toggle functionality
            $(document).on('click', '.toggle-status', function() {
                const id = $(this).data('id');
                const button = $(this);
                const originalHtml = button.html();

                button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Updating...');

                $.ajax({
                    url: `/admin/contact/${id}/status`,
                    type: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        if (response.status) {
                            // Update button appearance
                            const isViewed = response.newStatus === 'Viewed';
                            const btnClass = isViewed ? 'btn-success' : 'btn-warning';
                            const icon = isViewed ? 'fa-eye' : 'fa-clock';

                            button.removeClass('btn-success btn-warning').addClass(btnClass);
                            button.html(`<i class="fas ${icon}"></i> ${response.newStatus}`);

                            updateStatusCounts();
                            showAlert('success', response.success);
                        }
                    },
                    error: function(xhr) {
                        button.html(originalHtml);
                        const errorMsg = xhr.responseJSON?.error || 'Failed to update status.';
                        showAlert('error', errorMsg);
                    },
                    complete: function() {
                        button.prop('disabled', false);
                    }
                });
            });

            // Delete functionality
            $(document).on('click', '.delete-button', function() {
                const id = $(this).data('id');
                const row = $(this).closest('tr');

                if (confirm('Are you sure you want to delete this contact message?')) {
                    $.ajax({
                        url: `/admin/contact/${id}`,
                        type: 'DELETE',
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        },
                        success: function(response) {
                            if (response.success) {
                                table.row(row).remove().draw();
                                updateStatusCounts();

                                // Show success message
                                showAlert('success', response.success);
                            }
                        },
                        error: function(xhr) {
                            const errorMsg = xhr.responseJSON?.error || 'Failed to delete contact message.';
                            showAlert('error', errorMsg);
                        }
                    });
                }
            });

            function showAlert(type, message) {
                const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
                const alertHtml = `
                    <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                        ${message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                `;
                
                $('.container-fluid').prepend(alertHtml);
                
                // Auto dismiss after 5 seconds
                setTimeout(function() {
                    $('.alert').fadeOut();
                }, 5000);
            }
        });
    </script>
@endsection
