<?php

namespace App\Http\Controllers\Admin;

use Exception;
use App\Models\FaqType;
use Illuminate\Support\Str;
use App\Helpers\FileHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Storage;
use Yajra\DataTables\Facades\DataTables;

class FaqTypeController extends Controller
{
    public function index(Request $request)
    {
        try {
            if ($request->ajax()) {
                $types = FaqType::orderBy('created_at', 'desc')->get();

                return DataTables::of($types)
                    ->addIndexColumn()

                    ->editColumn('name', fn($t) => ucwords($t->name ?? '-'))

                    ->addColumn('image', function ($t) {
                        if ($t->image) {
                            $url = asset($t->image);
                            return '<img src="' . $url . '" alt="Image" width="50" height="50" class="rounded">';
                        }
                        return '<span class="text-muted">No Image</span>';
                    })

                    ->addColumn('actions', function ($t) {
                        $id = $t->faq_types_id;
                        $editRoute = route('faq-type.edit', $id);
                        $deleteBtn = '<button type="button" class="text-danger bg-transparent border-0 delete-button ttt" data-id="' . $id . '"><i class="fas fa-trash"></i></button>';
                        return '<div class="d-flex gap-2">
                                <a href="' . $editRoute . '" class="text-primary ttt"><i class="fas fa-edit"></i></a>
                                ' . $deleteBtn . '
                            </div>';
                    })

                    ->rawColumns(['image', 'actions'])
                    ->make(true);
            }

            return view('admin.faq-type.index');
        } catch (Exception $e) {
            Log::error('Error fetching FAQ Types: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Something went wrong while loading the FAQ Types.');
        }
    }

    public function create()
    {
        return view('admin.faq-type.create');
    }

    public function store(Request $request)
    {
        try {
            $validated = $request->validate([
                'name'  => 'required|string|max:50|unique:faq_types,name',
                'image' => 'required|image|mimes:jpg,jpeg,png,webp|max:5120',
            ]);

            $imagePath = null;
            if ($request->hasFile('image')) {
                $file = $request->file('image');
                $imagePath = FileHelper::uploadFiles($file, 'faq_types', 'public');
            }

            $type = new FaqType([
                'faq_types_id' => (string) Str::uuid(),
                'name'         => $validated['name'],
                'image'        => $imagePath,
            ]);

            $type->save();

            return redirect()->route('faq-type.index')->with('success', 'FAQ Type created successfully.');
        } catch (Exception $e) {
            Log::error('Failed to store FAQ Type', ['error' => $e->getMessage()]);
            return redirect()->back()->with('error', 'Something went wrong. Please try again.');
        }
    }

    public function edit(string $id)
    {
        $faqType = FaqType::where('faq_types_id', $id)->firstOrFail();
        return view('admin.faq-type.edit', compact('faqType'));
    }

    public function update(Request $request, string $id)
    {
        try {
            $type = FaqType::where('faq_types_id', $id)->firstOrFail();

            $validated = $request->validate([
                'name'  => 'required|string|max:50|unique:faq_types,name,' . $id . ',faq_types_id',
                'image' => 'nullable|image|mimes:jpg,jpeg,png,webp|max:5120',
            ]);

            $type->name = $validated['name'];

            if ($request->hasFile('image')) {
                if ($type->image && Storage::disk('public')->exists($type->image)) {
                    Storage::disk('public')->delete($type->image);
                }

                $file = $request->file('image');
                $uploadedPath = FileHelper::uploadFiles($file, 'faq-types', 'public');
                $type->image = $uploadedPath;
            }

            $type->save();

            return redirect()->route('faq-type.index')->with('success', 'FAQ Type updated successfully.');
        } catch (Exception $e) {
            Log::error('Failed to update FAQ Type', ['error' => $e->getMessage()]);
            return redirect()->back()->with('error', 'Something went wrong. Please try again.');
        }
    }

    public function destroy($id)
    {
        try {
            $type = FaqType::where('faq_types_id', $id)->firstOrFail();
            $type->delete();

            return response()->json(['status' => true, 'message' => 'FAQ Type deleted successfully.']);
        } catch (Exception $e) {
            Log::error('Failed to delete FAQ Type', ['id' => $id, 'error' => $e->getMessage()]);
            return response()->json(['status' => false, 'message' => 'Something went wrong.'], 500);
        }
    }
}
