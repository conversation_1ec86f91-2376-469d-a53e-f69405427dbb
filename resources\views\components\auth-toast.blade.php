<style>
    .custom-toast,
    .custom-toast-error,
    .custom-toast-info {
        backdrop-filter: blur(10px);
        background: rgba(40, 40, 40, 0.75);
        border: 1px solid rgba(255, 255, 255, 0.1);
        animation: slideIn 0.4s ease;
        position: relative;
    }

    .custom-toast::before,
    .custom-toast-error::before,
    .custom-toast-info::before {
        content: '';
        position: absolute;
        top: -1px;
        left: -1px;
        right: -1px;
        bottom: -1px;
        border-radius: inherit;
        border: 1px solid rgba(255, 255, 255, 0.08);
        pointer-events: none;
    }

    @keyframes slideIn {
        from {
            opacity: 0;
            transform: translateX(100%) scale(0.95);
        }

        to {
            opacity: 1;
            transform: translateX(0) scale(1);
        }
    }

    .toast-icon {
        font-size: 1.5rem;
        margin-right: 12px;
        display: flex;
        align-items: center;
    }

    .custom-toast {
        border-left: 5px solid #4ade80;
        /* Green */
    }

    .custom-toast-error {
        border-left: 5px solid #f87171;
        /* Red */
    }

    .custom-toast-info {
        border-left: 5px solid #60a5fa;
        /* Blue */
    }

    .btn-close-white {
        filter: invert(1);
    }
</style>

<!-- 🌟 Toast Message Wrapper -->
<div class="position-fixed top-0 end-0 p-3" style="z-index: 1080; max-width: 360px;">

    {{-- ✅ Success Toast --}}
    @if (session('success'))
        <div class="toast custom-toast text-white rounded-4 shadow-lg mb-3" role="alert" aria-live="assertive"
            aria-atomic="true">
            <div class="d-flex align-items-center p-3">
                <div class="toast-icon">✅</div>
                <div class="toast-body fw-medium">
                    {{ session('success') }}
                </div>
                <button type="button" class="btn-close btn-close-white ms-auto" data-bs-dismiss="toast"
                    aria-label="Close"></button>
            </div>
        </div>
    @endif

    {{-- ❌ Error Toast --}}
    @if (session('error'))
        <div class="toast custom-toast-error text-white rounded-4 shadow-lg mb-3" role="alert" aria-live="assertive"
            aria-atomic="true">
            <div class="d-flex align-items-center p-3">
                <div class="toast-icon">❌</div>
                <div class="toast-body fw-medium">
                    {{ session('error') }}
                </div>
                <button type="button" class="btn-close btn-close-white ms-auto" data-bs-dismiss="toast"
                    aria-label="Close"></button>
            </div>
        </div>
    @endif

    {{-- ℹ️ Info Toast --}}
    @if (session('info'))
        <div class="toast custom-toast-info text-white rounded-4 shadow-lg mb-3" role="alert" aria-live="assertive"
            aria-atomic="true">
            <div class="d-flex align-items-center p-3">
                <div class="toast-icon">ℹ️</div>
                <div class="toast-body fw-medium">
                    {{ session('info') }}
                </div>
                <button type="button" class="btn-close btn-close-white ms-auto" data-bs-dismiss="toast"
                    aria-label="Close"></button>
            </div>
        </div>
    @endif

    {{-- ✅ Toast Success (for admin operations) --}}
    @if (session('toast_success'))
        <div class="toast custom-toast text-white rounded-4 shadow-lg mb-3" role="alert" aria-live="assertive"
            aria-atomic="true">
            <div class="d-flex align-items-center p-3">
                <div class="toast-icon">✅</div>
                <div class="toast-body fw-medium">
                    {{ session('toast_success') }}
                </div>
                <button type="button" class="btn-close btn-close-white ms-auto" data-bs-dismiss="toast"
                    aria-label="Close"></button>
            </div>
        </div>
    @endif

    {{-- ❌ Toast Error (for admin operations) --}}
    @if (session('toast_error'))
        <div class="toast custom-toast-error text-white rounded-4 shadow-lg mb-3" role="alert" aria-live="assertive"
            aria-atomic="true">
            <div class="d-flex align-items-center p-3">
                <div class="toast-icon">❌</div>
                <div class="toast-body fw-medium">
                    {{ session('toast_error') }}
                </div>
                <button type="button" class="btn-close btn-close-white ms-auto" data-bs-dismiss="toast"
                    aria-label="Close"></button>
            </div>
        </div>
    @endif

</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        document.querySelectorAll('.toast').forEach(function(toastEl) {
            new bootstrap.Toast(toastEl, {
                delay: 4000,
                autohide: true
            }).show();
        });
    });
</script>
