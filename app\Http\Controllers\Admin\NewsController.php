<?php

namespace App\Http\Controllers\Admin;

use Exception;
use App\Models\News;
use Illuminate\Support\Str;
use App\Helpers\FileHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Storage;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Support\Facades\Validator;

class NewsController extends Controller
{
    public function index(Request $request)
    {
        try {
            if ($request->ajax()) {
                $news = News::select('news_id', 'name', 'slug', 'image', 'status', 'date')
                    ->orderByDesc('updated_at')
                    ->get();

                return DataTables::of($news)
                    ->addIndexColumn()
                    ->editColumn('image', function ($item) {
                        $img = $item->image ? asset($item->image) : asset('assets/images/default.jpg');
                        return '<img src="' . e($img) . '" alt="Image" width="50" height="50" class="rounded">';
                    })
                    ->editColumn('status', function ($item) {
                        $status = $item->status ? 'Active' : 'Inactive';
                        $btnClass = $item->status ? 'btn-success' : 'btn-secondary';
                        return '<button type="button" class="m-0 bdr btn btn-sm toggle-status ' . $btnClass . '" data-id="' . e($item->news_id) . '">' . $status . '</button>';
                    })
                    ->editColumn('date', fn($item) => optional($item->date)->format('d M, Y'))
                    ->addColumn('actions', function ($item) {
                        $id = e($item->news_id);
                        $editUrl = route('news.edit', $id);
                        $showUrl = route('news.show', $id);

                        return '<div class="d-flex gap-2">
                            <a href="' . $showUrl . '" class="text-info ttt" title="View"><i class="fas fa-eye"></i></a>
                            <a href="' . $editUrl . '" class="text-primary ttt" title="Edit"><i class="fas fa-edit"></i></a>
                            <button type="button" class="text-danger border-0 bg-transparent p-0 m-0 delete-button ttt" data-id="' . $id . '" title="Delete">
                                <i class="fas fa-trash"></i>
                            </button></div>';
                    })
                    ->rawColumns(['image', 'status', 'actions'])
                    ->make(true);
            }

            return view('admin.news.index');
        } catch (Exception $e) {
            Log::error('Error fetching news data: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Something went wrong while loading the news data.');
        }
    }

    public function create()
    {
        return view('admin.news.create');
    }

    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'name'        => 'required|string|max:255',
                'slug'        => 'nullable|string|max:255|unique:news,slug',
                'image'       => 'required|image|mimes:jpg,jpeg,png,webp|max:5120',
                'description' => 'required|string',
                'date'        => 'required|date',
                'status'      => 'required|in:0,1',
            ]);

            if ($validator->fails()) {
                return redirect()->back()->withErrors($validator)->withInput();
            }

            $validated = $validator->validated();

            $news = new News();
            $news->name        = $validated['name'];
            $news->slug        = Str::slug($validated['slug'] ?? $validated['name']);
            $news->description = $validated['description'];
            $news->date        = $validated['date'];
            $news->status      = $validated['status'];

            if ($request->hasFile('image')) {
                $news->image = FileHelper::uploadFiles($request->file('image'), 'news', 'public');
                Log::info('News image uploaded.', ['path' => $news->image]);
            }

            $news->save();

            return redirect()->route('news.index')->with('success', 'News created successfully.');
        } catch (Exception $e) {
            Log::error('Failed to store news.', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return redirect()->back()->with('error', 'Something went wrong. Please try again.');
        }
    }

    public function show(string $id)
    {
        $news = News::where('news_id', $id)->firstOrFail();
        return view('admin.news.show', compact('news'));
    }

    public function edit(string $id)
    {
        $news = News::where('news_id', $id)->firstOrFail();
        return view('admin.news.edit', compact('news'));
    }

    public function update(Request $request, string $id)
    {
        try {
            $news = News::where('news_id', $id)->firstOrFail();

            $validator = Validator::make($request->all(), [
                'name'        => 'required|string|max:255',
                'slug'        => 'nullable|string|max:255|unique:news,slug,' . $id . ',news_id',
                'image'       => 'nullable|image|mimes:jpg,jpeg,png,webp|max:5120',
                'description' => 'required|string',
                'date'        => 'required|date',
                'status'      => 'required|in:0,1',
            ]);

            if ($validator->fails()) {
                return redirect()->back()->withErrors($validator)->withInput();
            }

            $validated = $validator->validated();

            $slug = Str::slug($validated['slug'] ?? $validated['name']);
            if ($slug !== $news->slug && News::where('slug', $slug)->where('news_id', '!=', $id)->exists()) {
                return redirect()->back()->with('error', 'The slug has already been taken.')->withInput();
            }

            $news->fill([
                'name'        => $validated['name'],
                'slug'        => $slug,
                'description' => $validated['description'],
                'date'        => $validated['date'],
                'status'      => $validated['status'],
            ]);

            if ($request->hasFile('image')) {
                if ($news->image && Storage::disk('public')->exists($news->image)) {
                    Storage::disk('public')->delete($news->image);
                }
                $news->image = FileHelper::uploadFiles($request->file('image'), 'news', 'public');
            }

            $news->save();

            return redirect()->route('news.index')->with('success', 'News updated successfully.');
        } catch (Exception $e) {
            Log::error('Failed to update news', ['error' => $e->getMessage()]);
            return redirect()->back()->with('error', 'Something went wrong. Please try again.')->withInput();
        }
    }

    public function destroy($id)
    {
        try {
            $news = News::where('news_id', $id)->firstOrFail();

            if ($news->image && Storage::disk('public')->exists($news->image)) {
                Storage::disk('public')->delete($news->image);
            }

            $news->delete();

            return response()->json(['status' => true, 'message' => 'News deleted successfully.']);
        } catch (Exception $e) {
            Log::error('Failed to delete news', ['error' => $e->getMessage()]);
            return response()->json(['status' => false, 'message' => 'Something went wrong.'], 500);
        }
    }

    public function status($id)
    {
        try {
            $news = News::where('news_id', $id)->firstOrFail();
            $news->status = !$news->status;
            $news->save();

            return response()->json([
                'status' => true,
                'newStatus' => $news->status ? 'Active' : 'Inactive'
            ]);
        } catch (Exception $e) {
            Log::error('Failed to toggle news status: ' . $e->getMessage());
            return response()->json(['status' => false], 500);
        }
    }

    public function upload(Request $request)
    {
        if ($request->hasFile('upload')) {
            $file = $request->file('upload');
            $filename = time() . '_' . $file->getClientOriginalName();

            $path = $file->storeAs('news', $filename, 'public');

            return response()->json(['url' => asset('storage/' . $path)]);
        }

        return response()->json(['url' => null], 400);
    }
}
