<?php

namespace App\Http\Controllers\Admin;

use Exception;
use App\Models\Banner;
use Illuminate\Support\Str;
use App\Helpers\FileHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Storage;
use Yajra\DataTables\Facades\DataTables;

class BannerController extends Controller
{
    public function index(Request $request)
    {
        try {
            if ($request->ajax()) {
                $banners = Banner::select('banners_id', 'type', 'media_path', 'status', 'created_at')
                    ->orderBy('created_at', 'desc')
                    ->get();

                return DataTables::of($banners)
                    ->addIndexColumn()
                    ->editColumn('media_path', function ($b) {
                        if ($b->isImage()) {
                            $img = $b->media_path ? asset($b->media_path) : asset('assets/images/default.jpg');
                            return '<img src="' . $img . '" alt="Image" width="170" height="110" class="rounded">';
                        } elseif ($b->isVideo()) {
                            return '<video width="170" height="120" controls>
                                    <source src="' . asset($b->media_path) . '" type="video/mp4">
                                    Your browser does not support the video tag.
                                </video>';
                        }
                        return '';
                    })
                    ->editColumn('status', function ($b) {
                        $status = $b->status ? 'Active' : 'Inactive';
                        $btnClass = $b->status ? 'btn-success' : 'btn-secondary';
                        return '<button type="button" class="m-0 bdr btn btn-sm toggle-status ' . $btnClass . '" data-id="' . $b->banners_id . '">' . $status . '</button>';
                    })
                    ->editColumn('created_at', fn($b) => optional($b->created_at)->format('d M, Y h:i A'))
                    ->addColumn('actions', function ($b) {
                        $id = $b->banners_id;
                        $editRoute = route('banner.edit', $id);
                        return '
                    <div class="d-flex gap-2">
                        <a href="' . $editRoute . '" class="text-primary ttt" title="Edit">
                            <i class="fas fa-edit"></i>
                        </a>
                        <button type="button" class="text-danger border-0 bg-transparent p-0 m-0 delete-button ttt" data-id="' . $id . '" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>';
                    })
                    ->rawColumns(['media_path', 'status', 'actions'])
                    ->make(true);
            }

            return view('admin.banner.index');
        } catch (Exception $e) {
            Log::error('Error fetching banner data: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Something went wrong while loading the data.');
        }
    }

    public function create()
    {
        return view('admin.banner.create');
    }

    public function store(Request $request)
    {
        try {
            $validated = $request->validate([
                'type'       => 'required|in:image,video',
                'media_file' => 'required|file|max:10240|mimetypes:image/jpeg,image/png,image/webp,video/mp4,video/quicktime',
                'status'     => 'required|in:0,1',
            ]);

            $file = $request->file('media_file');
            $mime = $file->getMimeType();
            $type = $request->input('type');

            // Custom check: mime must match declared type
            if (
                ($type === 'image' && !str_starts_with($mime, 'image/')) ||
                ($type === 'video' && !str_starts_with($mime, 'video/'))
            ) {
                return redirect()->back()
                    ->withErrors(['media_file' => "Uploaded file must be a valid {$type}."])
                    ->withInput();
            }

            $banner = new Banner([
                'banners_id' => (string) Str::uuid(),
                'type'       => $type,
                'status'     => $validated['status'],
                'media_path' => FileHelper::uploadFiles($file, 'banners', 'public'),
            ]);

            $banner->save();

            return redirect()->route('banner.index')->with('success', 'Banner created successfully.');
        } catch (Exception $e) {
            Log::error('Failed to store banner', [
                'error' => $e->getMessage(),
                'file'  => $e->getFile(),
                'line'  => $e->getLine(),
            ]);
            return redirect()->back()->with('error', 'Something went wrong. Please try again.');
        }
    }

    public function edit(string $id)
    {
        $banner = Banner::where('banners_id', $id)->firstOrFail();
        return view('admin.banner.edit', compact('banner'));
    }

    public function update(Request $request, string $id)
    {
        try {
            $banner = Banner::where('banners_id', $id)->firstOrFail();

            $request->validate([
                'type'       => 'required|in:image,video',
                'media_file' => 'nullable|file|max:10240|mimetypes:image/jpeg,image/png,image/webp,video/mp4,video/quicktime',
                'status'     => 'required|in:0,1',
            ]);

            if ($request->hasFile('media_file')) {
                $mime = $request->file('media_file')->getMimeType();
                $type = $request->input('type');

                if (
                    ($type === 'image' && !str_starts_with($mime, 'image/')) ||
                    ($type === 'video' && !str_starts_with($mime, 'video/'))
                ) {
                    return redirect()->back()
                        ->withErrors(['media_file' => "Uploaded file must be a valid {$type}."])
                        ->withInput();
                }

                // Delete old file
                if ($banner->media_path && Storage::disk('public')->exists($banner->media_path)) {
                    Storage::disk('public')->delete($banner->media_path);
                }

                // Upload new file
                $banner->media_path = FileHelper::uploadFiles($request->file('media_file'), 'banners', 'public');
            }

            // Update banner fields
            $banner->type = $request->type;
            $banner->status = $request->status;
            $banner->save();

            return redirect()->route('banner.index')->with('success', 'Banner updated successfully.');
        } catch (Exception $e) {
            Log::error('Failed to update banner', [
                'error' => $e->getMessage(),
                'file'  => $e->getFile(),
                'line'  => $e->getLine(),
            ]);
            return redirect()->back()->with('error', 'Something went wrong. Please try again.');
        }
    }

    public function destroy($id)
    {
        try {
            $banner = Banner::where('banners_id', $id)->firstOrFail();

            if ($banner->media_path) {
                FileHelper::deleteFile($banner->media_path, 'public');
            }

            $banner->delete();

            return response()->json(['status' => true, 'message' => 'Banner deleted successfully.']);
        } catch (Exception $e) {
            return response()->json(['status' => false, 'message' => 'Something went wrong.'], 500);
        }
    }

    public function status($id)
    {
        try {
            $banner = Banner::where('banners_id', $id)->firstOrFail();
            $banner->status = !$banner->status;
            $banner->save();

            return response()->json([
                'status'     => true,
                'newStatus'  => $banner->status ? 'Active' : 'Inactive'
            ]);
        } catch (Exception $e) {
            Log::error('Status toggle failed: ' . $e->getMessage());
            return response()->json(['status' => false], 500);
        }
    }
}
