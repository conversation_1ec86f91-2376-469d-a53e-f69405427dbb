<?php

namespace App\Http\Controllers\Admin;

use Exception;
use App\Models\Faq;
use App\Models\FaqType;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Ya<PERSON>ra\DataTables\Facades\DataTables;

class FaqController extends Controller
{
    public function index(Request $request)
    {
        try {
            if ($request->ajax()) {
                $faqs = Faq::with(['type:faq_types_id,name']) 
                    ->select('faqs_id', 'faq_types_id', 'question', 'status', 'created_at')
                    ->when($request->faq_types_id, function ($query) use ($request) {
                        $query->where('faq_types_id', $request->faq_types_id);
                    })
                    ->orderByDesc('created_at');

                return DataTables::of($faqs)
                    ->addIndexColumn()
                    ->addColumn('type', function ($faq) {
                        return ucwords(optional($faq->type)->name ?? '-');
                    })
                    ->editColumn('question', function ($faq) {
                        return ucfirst($faq->question ?? '-');
                    })
                    ->addColumn('status', function ($faq) {
                        $statusText = $faq->status ? 'Active' : 'Inactive';
                        $buttonClass = $faq->status ? 'btn-success' : 'btn-secondary';
                        return <<<HTML
                        <button type="button" class=" m-0 bdr btn btn-sm toggle-status {$buttonClass}" data-id="{$faq->faqs_id}">
                            {$statusText}
                        </button>
                    HTML;
                    })
                    ->addColumn('actions', function ($faq) {
                        $showUrl = route('faq.show', ['faq' => $faq->faqs_id]);
                        $editUrl = route('faq.edit', ['faq' => $faq->faqs_id]);

                        return <<<HTML
                        <div class="d-flex gap-2">
                            <a href="{$showUrl}" class="text-info ttt"><i class="fas fa-eye"></i></a>
                            <a href="{$editUrl}" class="text-primary ttt"><i class="fas fa-edit"></i></a>
                            <button type="button" class="text-danger bg-transparent border-0 delete-button ttt" data-id="{$faq->faqs_id}">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    HTML;
                    })
                    ->rawColumns(['status', 'actions'])
                    ->make(true);
            }

            return view('admin.faq.index');
        } catch (Exception $e) {
            Log::error('FAQ Index Error: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Unable to load FAQ data.');
        }
    }

    public function create()
    {
        $faqTypes = FaqType::get();
        return view('admin.faq.create', compact('faqTypes'));
    }

    public function store(Request $request)
    {
        try {
            $validated = $request->validate([
                'faq_types_id' => 'required|exists:faq_types,faq_types_id',
                'question'     => 'required|string|max:255',
                'answer'       => 'required|string',
                'status'       => 'required|in:0,1',
            ]);

            Faq::create([
                'faqs_id'      => (string) Str::uuid(),
                'faq_types_id' => $validated['faq_types_id'],
                'question'     => $validated['question'],
                'answer'       => $validated['answer'],
                'status'       => $validated['status'],
            ]);

            return redirect()->route('faq.index')->with('success', 'FAQ created successfully.');
        } catch (Exception $e) {
            Log::error('Failed to store FAQ', ['error' => $e->getMessage()]);
            return redirect()->back()
                ->withInput()
                ->with('error', 'Something went wrong. Please try again.');
        }
    }

    public function show(string $id)
    {
        $faq = Faq::with('type:faq_types_id,name')
            ->where('faqs_id', $id)
            ->firstOrFail();

        return view('admin.faq.show', compact('faq'));
    }

    public function edit(string $id)
    {
        $faq = Faq::with('type:faq_types_id,name')
            ->where('faqs_id', $id)
            ->firstOrFail();

        $types = FaqType::select('faq_types_id', 'name')->get();

        return view('admin.faq.edit', compact('faq', 'types'));
    }

    public function update(Request $request, string $id)
    {
        try {
            $faq = Faq::where('faqs_id', $id)->firstOrFail();

            $validated = $request->validate([
                'faq_types_id' => 'required|exists:faq_types,faq_types_id',
                'question'     => 'required|string|max:255',
                'answer'       => 'required|string',
                'status'       => 'required|in:0,1',
            ]);

            $faq->update($validated);

            return redirect()->route('faq.index')->with('success', 'FAQ updated successfully.');
        } catch (Exception $e) {
            Log::error('FAQ Update Error', [
                'faq_id' => $id,
                'error'  => $e->getMessage()
            ]);

            return redirect()->back()
                ->withInput()
                ->with('error', 'Something went wrong. Please try again.');
        }
    }

    public function destroy($id)
    {
        try {
            $faq = Faq::where('faqs_id', $id)->firstOrFail();
            $faq->delete();

            return response()->json(['status' => true, 'message' => 'FAQ deleted successfully.']);
        } catch (Exception $e) {
            Log::error('Failed to delete FAQ', ['id' => $id, 'error' => $e->getMessage()]);
            return response()->json(['status' => false, 'message' => 'Something went wrong.'], 500);
        }
    }

    public function status($id)
    {
        try {
            $faq = Faq::where('faqs_id', $id)->firstOrFail();
            $faq->status = !$faq->status;
            $faq->save();

            return response()->json([
                'status' => true,
                'newStatus' => $faq->status ? 'Active' : 'Inactive'
            ]);
        } catch (Exception $e) {
            Log::error('Failed to toggle status: ' . $e->getMessage());
            return response()->json(['status' => false], 500);
        }
    }
}
