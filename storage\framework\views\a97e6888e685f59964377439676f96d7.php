<?php $__env->startSection('content'); ?>
    <?php echo $__env->make('layouts.navbars.auth.topnav', ['title' => 'Contact Messages'], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <div class="container-fluid py-4">
        <div class="row justify-content-center">
            <div class="col-lg-12 col-md-12">
                <div class="card mb-4">
                    <div class="d-flex justify-content-between px-3 py-4">
                        <h6 class="mb-0" style="color: #67748e;">Contact Messages</h6>
                    </div>

                    <div class="card-body p-3">
                        <?php if (isset($component)) { $__componentOriginalc8463834ba515134d5c98b88e1a9dc03 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc8463834ba515134d5c98b88e1a9dc03 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.data-table','data' => ['id' => 'contactsTable','ajax' => route('contact.index'),'columns' => [
                            [
                                'data' => 'DT_RowIndex',
                                'name' => 'DT_RowIndex',
                                'orderable' => false,
                                'searchable' => false,
                            ],
                            ['data' => 'full_name', 'name' => 'full_name'],
                            ['data' => 'email', 'name' => 'email'],
                            ['data' => 'phone', 'name' => 'phone'],
                            ['data' => 'subject', 'name' => 'subject'],
                            ['data' => 'status', 'name' => 'status'],
                            ['data' => 'created_at', 'name' => 'created_at'],
                            ['data' => 'actions', 'name' => 'actions', 'orderable' => false, 'searchable' => false],
                        ],'order' => []]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('data-table'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['id' => 'contactsTable','ajax' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('contact.index')),'columns' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
                            [
                                'data' => 'DT_RowIndex',
                                'name' => 'DT_RowIndex',
                                'orderable' => false,
                                'searchable' => false,
                            ],
                            ['data' => 'full_name', 'name' => 'full_name'],
                            ['data' => 'email', 'name' => 'email'],
                            ['data' => 'phone', 'name' => 'phone'],
                            ['data' => 'subject', 'name' => 'subject'],
                            ['data' => 'status', 'name' => 'status'],
                            ['data' => 'created_at', 'name' => 'created_at'],
                            ['data' => 'actions', 'name' => 'actions', 'orderable' => false, 'searchable' => false],
                        ]),'order' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([])]); ?>
                             <?php $__env->slot('header', null, []); ?> 
                                <th>S.No.</th>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Phone</th>
                                <th>Subject</th>
                                <th>Status</th>
                                <th>Date</th>
                                <th>Actions</th>
                             <?php $__env->endSlot(); ?>
                         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc8463834ba515134d5c98b88e1a9dc03)): ?>
<?php $attributes = $__attributesOriginalc8463834ba515134d5c98b88e1a9dc03; ?>
<?php unset($__attributesOriginalc8463834ba515134d5c98b88e1a9dc03); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc8463834ba515134d5c98b88e1a9dc03)): ?>
<?php $component = $__componentOriginalc8463834ba515134d5c98b88e1a9dc03; ?>
<?php unset($__componentOriginalc8463834ba515134d5c98b88e1a9dc03); ?>
<?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>
        $(document).ready(function() {
            const table = $('#contactsTable').DataTable();

            // Update counts after table loads
            table.on('draw', function() {
                updateStatusCounts();
            });

            function updateStatusCounts() {
                let pendingCount = 0;
                let viewedCount = 0;
                
                table.rows().every(function() {
                    const data = this.data();
                    if (data.status && data.status.includes('Pending')) {
                        pendingCount++;
                    } else if (data.status && data.status.includes('Viewed')) {
                        viewedCount++;
                    }
                });
                
                $('#pending-count').text(pendingCount);
                $('#viewed-count').text(viewedCount);
            }

            // Status toggle functionality
            $(document).on('click', '.toggle-status', function() {
                const id = $(this).data('id');
                const button = $(this);
                const originalHtml = button.html();

                button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Updating...');

                $.ajax({
                    url: `/admin/contact/${id}/status`,
                    type: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        if (response.status) {
                            // Update button appearance
                            const isViewed = response.newStatus === 'Viewed';
                            const btnClass = isViewed ? 'btn-success' : 'btn-warning';
                            const icon = isViewed ? 'fa-eye' : 'fa-clock';

                            button.removeClass('btn-success btn-warning').addClass(btnClass);
                            button.html(`<i class="fas ${icon}"></i> ${response.newStatus}`);

                            updateStatusCounts();
                            showAuthToast('success', response.success);
                        }
                    },
                    error: function(xhr) {
                        button.html(originalHtml);
                        const errorMsg = xhr.responseJSON?.error || 'Failed to update status.';
                        showAuthToast('error', errorMsg);
                    },
                    complete: function() {
                        button.prop('disabled', false);
                    }
                });
            });

            // Delete functionality
            $(document).on('click', '.delete-button', function() {
                const id = $(this).data('id');
                const row = $(this).closest('tr');

                if (confirm('Are you sure you want to delete this contact message?')) {
                    $.ajax({
                        url: `/admin/contact/${id}`,
                        type: 'DELETE',
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        },
                        success: function(response) {
                            if (response.success) {
                                table.row(row).remove().draw();
                                updateStatusCounts();

                                // Show success message
                                showAuthToast('success', response.success);
                            }
                        },
                        error: function(xhr) {
                            const errorMsg = xhr.responseJSON?.error || 'Failed to delete contact message.';
                            showAuthToast('error', errorMsg);
                        }
                    });
                }
            });

            // Function to show auth-style toasts dynamically
            function showAuthToast(type, message) {
                const toastContainer = $('.position-fixed.top-0.end-0');
                let toastClass, icon;

                if (type === 'success') {
                    toastClass = 'custom-toast';
                    icon = '✅';
                } else {
                    toastClass = 'custom-toast-error';
                    icon = '❌';
                }

                const toastHtml = `
                    <div class="toast ${toastClass} text-white rounded-4 shadow-lg mb-3" role="alert" aria-live="assertive" aria-atomic="true">
                        <div class="d-flex align-items-center p-3">
                            <div class="toast-icon">${icon}</div>
                            <div class="toast-body fw-medium">${message}</div>
                            <button type="button" class="btn-close btn-close-white ms-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                        </div>
                    </div>
                `;

                toastContainer.append(toastHtml);

                // Initialize and show the toast
                const newToast = toastContainer.find('.toast').last();
                new bootstrap.Toast(newToast[0], {
                    delay: 4000,
                    autohide: true
                }).show();

                // Remove toast element after it's hidden
                newToast.on('hidden.bs.toast', function() {
                    $(this).remove();
                });
            }

        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\trash\resources\views/admin/contact/index.blade.php ENDPATH**/ ?>