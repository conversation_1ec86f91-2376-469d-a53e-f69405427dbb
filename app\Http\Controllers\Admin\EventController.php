<?php

namespace App\Http\Controllers\Admin;

use Exception;
use App\Models\Event;
use App\Models\EventMedia;
use Illuminate\Support\Str;
use App\Helpers\FileHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Carbon\Carbon;
use Illuminate\Support\Facades\Storage;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Support\Facades\Validator;

class EventController extends Controller
{
    public function index(Request $request)
    {
        try {
            if ($request->ajax()) {
                $events = Event::select('events_id', 'title', 'slug', 'cover_image', 'status', 'start_datetime')
                    ->orderByDesc('updated_at')
                    ->get();

                return DataTables::of($events)
                    ->addIndexColumn()
                    ->editColumn('cover_image', function ($event) {
                        $img = $event->cover_image ? asset($event->cover_image) : asset('assets/images/default.jpg');
                        return '<img src="' . e($img) . '" alt="Image" width="50" height="50" class="rounded">';
                    })
                    ->editColumn('status', function ($event) {
                        $status = $event->status;
                        $btnClass = match ($status) {
                            'upcoming' => 'btn-primary',
                            'ongoing'  => 'btn-success',
                            'completed' => 'btn-secondary',
                            'cancelled' => 'btn-danger',
                        };
                        return '<button type="button" class="m-0 bdr btn btn-sm toggle-status ' . $btnClass . '" data-id="' . e($event->events_id) . '">' . ucfirst($status) . '</button>';
                    })
                    ->editColumn('start_datetime', fn($event) => $event->start_datetime ? Carbon::parse($event->start_datetime)->format('d M, Y') : '')
                    ->addColumn('actions', function ($event) {
                        $id = e($event->events_id);
                        $editUrl = route('events.edit', $id);
                        $showUrl = route('events.show', $id);

                        return '
                        <div class="d-flex gap-2">
                            <a href="' . $showUrl . '" class="text-info ttt" title="View"><i class="fas fa-eye"></i></a>
                            <a href="' . $editUrl . '" class="text-primary ttt" title="Edit"><i class="fas fa-edit"></i></a>
                            <button type="button" class="text-danger border-0 bg-transparent p-0 m-0 delete-button ttt" data-id="' . $id . '" title="Delete">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>';
                    })
                    ->rawColumns(['cover_image', 'status', 'actions'])
                    ->make(true);
            }

            return view('admin.events.index');
        } catch (Exception $e) {
            Log::error('Error fetching event data: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Something went wrong while loading the event data.');
        }
    }

    public function create()
    {
        return view('admin.events.create');
    }

    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'title'          => 'required|string|max:255',
                'slug'           => 'nullable|string|max:255|unique:events,slug',
                'summary'        => 'required|string|max:500',
                'description'    => 'required|string',
                'category'       => 'nullable|string|max:255',
                'status'         => 'required|in:upcoming,ongoing,completed,cancelled',
                'start_datetime' => 'required|date',
                'end_datetime'   => 'required|date|after_or_equal:start_datetime',
                'cover_image'    => 'required|image|mimes:jpg,jpeg,png,webp|max:5120',
                'venue_name'     => 'required|string|max:255',
                'address'        => 'required|string|max:500',
                'city'           => 'required|string|max:100',
                'state'          => 'required|string|max:100',
                'pincode'        => 'required|string|max:10',
                'google_map_link' => 'required|url|max:500',
                'organizer_name'  => 'required|string|max:255',
                'organizer_email' => 'required|email|max:255',
                'organizer_phone' => 'required|string|max:20',
                'media_files.*'   => 'nullable|file|mimes:jpg,jpeg,png,webp,mp4,avi,mov|max:10240',
            ]);

            if ($validator->fails()) {
                return redirect()->back()->withErrors($validator)->withInput();
            }

            $validated = $validator->validated();

            $event = new Event();
            $event->title          = $validated['title'];
            $event->slug           = Str::slug($validated['slug'] ?? $validated['title']);
            $event->summary        = $validated['summary'];
            $event->description    = $validated['description'];
            $event->category       = $validated['category'] ?? null;
            $event->status         = $validated['status'];
            $event->start_datetime = $validated['start_datetime'];
            $event->end_datetime   = $validated['end_datetime'] ?? null;
            $event->venue_name     = $validated['venue_name'] ?? null;
            $event->address        = $validated['address'] ?? null;
            $event->city           = $validated['city'] ?? null;
            $event->state          = $validated['state'] ?? null;
            $event->pincode        = $validated['pincode'] ?? null;
            $event->google_map_link = $validated['google_map_link'] ?? null;
            $event->organizer_name = $validated['organizer_name'] ?? null;
            $event->organizer_email = $validated['organizer_email'] ?? null;
            $event->organizer_phone = $validated['organizer_phone'] ?? null;

            if ($request->hasFile('cover_image')) {
                $event->cover_image = FileHelper::uploadFiles($request->file('cover_image'), 'events', 'public');
            }

            $event->save();

            // Handle multiple media files
            if ($request->hasFile('media_files')) {
                foreach ($request->file('media_files') as $file) {
                    $filePath = FileHelper::uploadFiles($file, 'events/media', 'public');
                    $fileExtension = strtolower($file->getClientOriginalExtension());
                    $type = in_array($fileExtension, ['mp4', 'avi', 'mov', 'wmv', 'flv']) ? 'video' : 'image';

                    EventMedia::create([
                        'events_id' => $event->events_id,
                        'file_path' => $filePath,
                        'type' => $type
                    ]);
                }
            }

            return redirect()->route('events.index')->with('success', 'Event created successfully.');
        } catch (Exception $e) {
            Log::error('Failed to store event.', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return redirect()->back()->with('error', 'Something went wrong. Please try again.');
        }
    }

    public function show(string $id)
    {
        $event = Event::with('media')->where('events_id', $id)->firstOrFail();
        return view('admin.events.show', compact('event'));
    }

    public function edit(string $id)
    {
        $event = Event::with('media')->where('events_id', $id)->firstOrFail();
        return view('admin.events.edit', compact('event'));
    }

    public function update(Request $request, string $id)
    {
        try {
            $event = Event::where('events_id', $id)->firstOrFail();

            $validator = Validator::make($request->all(), [
                'title'            => 'required|string|max:255',
                'slug'             => 'nullable|string|max:255|unique:events,slug,' . $id . ',events_id',
                'summary'          => 'required|string|max:500',
                'description'      => 'required|string',
                'category'         => 'nullable|string|max:255',
                'status'           => 'required|in:upcoming,ongoing,completed,cancelled',
                'start_datetime'   => 'required|date',
                'end_datetime'     => 'required|date|after_or_equal:start_datetime',
                'cover_image'      => 'nullable|image|mimes:jpg,jpeg,png,webp|max:5120',
                'venue_name'       => 'required|string|max:255',
                'address'          => 'required|string|max:500',
                'city'             => 'required|string|max:100',
                'state'            => 'required|string|max:100',
                'pincode'          => 'required|string|max:10',
                'google_map_link'  => 'required|url|max:500',
                'organizer_name'   => 'required|string|max:255',
                'organizer_email'  => 'required|email|max:255',
                'organizer_phone'  => 'required|string|max:20',
                'media_files.*'    => 'nullable|file|mimes:jpg,jpeg,png,webp,mp4,avi,mov|max:10240',
                'remove_media'     => 'nullable|array',
                'remove_media.*'   => 'string',
            ]);

            if ($validator->fails()) {
                return redirect()->back()->withErrors($validator)->withInput();
            }

            $validated = $validator->validated();
            $slug = Str::slug($validated['slug'] ?? $validated['title']);

            if ($slug !== $event->slug && Event::where('slug', $slug)->where('events_id', '!=', $id)->exists()) {
                return redirect()->back()->with('error', 'The slug has already been taken.');
            }

            // Update event fields
            $event->fill([
                'title'            => $validated['title'],
                'slug'             => $slug,
                'summary'          => $validated['summary'],
                'description'      => $validated['description'],
                'category'         => $validated['category'] ?? null,
                'status'           => $validated['status'],
                'start_datetime'   => $validated['start_datetime'],
                'end_datetime'     => $validated['end_datetime'] ?? null,
                'venue_name'       => $validated['venue_name'] ?? null,
                'address'          => $validated['address'] ?? null,
                'city'             => $validated['city'] ?? null,
                'state'            => $validated['state'] ?? null,
                'pincode'          => $validated['pincode'] ?? null,
                'google_map_link'  => $validated['google_map_link'] ?? null,
                'organizer_name'   => $validated['organizer_name'] ?? null,
                'organizer_email'  => $validated['organizer_email'] ?? null,
                'organizer_phone'  => $validated['organizer_phone'] ?? null,
            ]);

            // Handle new cover image
            if ($request->hasFile('cover_image')) {
                // Delete old cover image if exists
                if ($event->cover_image && Storage::disk('public')->exists($event->cover_image)) {
                    Storage::disk('public')->delete($event->cover_image);
                }
                $event->cover_image = FileHelper::uploadFiles($request->file('cover_image'), 'events', 'public');
            }

            // Save the event first
            $event->save();

            // Handle media removal
            if ($request->has('remove_media')) {
                $removeMedia = $request->input('remove_media');

                // Ensure it's an array
                if (!is_array($removeMedia)) {
                    $removeMedia = [$removeMedia];
                }

                foreach ($removeMedia as $mediaId) {
                    $media = EventMedia::where('event_media_id', $mediaId)
                        ->where('events_id', $event->events_id)
                        ->first();

                    if ($media) {
                        // Delete file from storage
                        if ($media->file_path && Storage::disk('public')->exists($media->file_path)) {
                            Storage::disk('public')->delete($media->file_path);
                        }
                        // Delete record from database
                        $media->delete();
                    }
                }
            }

            // Handle new media uploads
            if ($request->hasFile('media_files')) {
                foreach ($request->file('media_files') as $file) {
                    $filePath = FileHelper::uploadFiles($file, 'events/media', 'public');
                    $fileExtension = strtolower($file->getClientOriginalExtension());
                    $type = in_array($fileExtension, ['mp4', 'avi', 'mov', 'wmv', 'flv']) ? 'video' : 'image';

                    EventMedia::create([
                        'events_id' => $event->events_id,
                        'file_path' => $filePath,
                        'type'      => $type,
                    ]);
                }
            }

            return redirect()->route('events.index')->with('success', 'Event updated successfully.');
        } catch (Exception $e) {
            Log::error('Failed to update event', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'event_id' => $id
            ]);
            return redirect()->back()->with('error', 'Something went wrong. Please try again.')->withInput();
        }
    }

    public function destroy($id)
    {
        try {
            $event = Event::with('media')->where('events_id', $id)->firstOrFail();

            if ($event->cover_image && Storage::disk('public')->exists($event->cover_image)) {
                Storage::disk('public')->delete($event->cover_image);
            }

            foreach ($event->media as $media) {
                if (Storage::disk('public')->exists($media->file_path)) {
                    Storage::disk('public')->delete($media->file_path);
                }
            }

            $event->delete();

            return response()->json(['status' => true, 'message' => 'Event deleted successfully.']);
        } catch (Exception $e) {
            Log::error('Failed to delete event', ['error' => $e->getMessage()]);
            return response()->json(['status' => false, 'message' => 'Something went wrong.'], 500);
        }
    }

    public function status(Request $request, $id)
    {
        try {
            $event = Event::where('events_id', $id)->firstOrFail();

            $validator = Validator::make($request->all(), [
                'status' => 'required|in:upcoming,ongoing,completed,cancelled'
            ]);

            if ($validator->fails()) {
                return response()->json(['status' => false, 'message' => 'Invalid status provided.'], 400);
            }

            $newStatus = $request->status;
            $event->status = $newStatus;
            $event->save();

            $btnClass = match ($newStatus) {
                'upcoming' => 'btn-primary',
                'ongoing'  => 'btn-success',
                'completed' => 'btn-secondary',
                'cancelled' => 'btn-danger',
            };

            return response()->json([
                'status' => true,
                'message' => 'Status updated successfully.',
                'newStatus' => ucfirst($newStatus),
                'btnClass' => $btnClass
            ]);
        } catch (Exception $e) {
            Log::error('Failed to update event status', ['error' => $e->getMessage()]);
            return response()->json(['status' => false, 'message' => 'Something went wrong.'], 500);
        }
    }
}
