<?php

namespace App\Http\Controllers\Admin;

use Exception;
use App\Models\Contact;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Yajra\DataTables\Facades\DataTables;

class ContactController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        try {
            if ($request->ajax()) {
                $contacts = Contact::select('contacts_id', 'first_name', 'last_name', 'email', 'phone', 'subject', 'status', 'created_at')
                    ->orderByDesc('created_at')
                    ->get();

                return DataTables::of($contacts)
                    ->addIndexColumn()

                    ->editColumn('full_name', fn($c) => ucwords(($c->first_name ?? '') . ' ' . ($c->last_name ?? '')))

                    ->editColumn('email', fn($c) => strtolower($c->email ?? ''))

                    ->editColumn('phone', fn($c) => $c->phone ?? '-')

                    ->editColumn('subject', fn($c) => ucwords(str_replace('-', ' ', $c->subject ?? '')))

                    ->editColumn('status', function ($c) {
                        $status = $c->status == 1 ? 'Viewed' : 'Pending';
                        $btnClass = $c->status == 1 ? 'btn-success' : 'btn-warning';
                        $icon = $c->status == 1 ? 'fa-eye' : 'fa-clock';

                        return '<button type="button" class="badge ' . $btnClass . ' border-0 toggle-status" data-id="' . e($c->contacts_id) . '">
                                    <i class="fas ' . $icon . '"></i> ' . $status . '
                                </button>';
                    })

                    ->editColumn('created_at', fn($c) => optional($c->created_at)->format('d M, Y h:i A'))

                    ->addColumn('actions', function ($c) {
                        $id = e($c->contacts_id);
                        $showUrl = route('contact.show', $id);

                        return '
                    <div class="d-flex gap-2">
                        <a href="' . $showUrl . '" class="text-info ttt" title="View"><i class="fas fa-eye"></i></a>
                        <button type="button" class="text-danger border-0 bg-transparent p-0 m-0 delete-button ttt" data-id="' . $id . '" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>';
                    })

                    ->rawColumns(['status', 'actions'])
                    ->make(true);
            }

            return view('admin.contact.index');
        } catch (Exception $e) {
            Log::error('Error fetching contact data: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Something went wrong while loading the contact data.');
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        try {
            $contact = Contact::where('contacts_id', $id)->firstOrFail();
            
            // Mark as viewed if it's pending
            if ($contact->status == 0) {
                $contact->update(['status' => 1]);
            }
            
            return view('admin.contact.show', compact('contact'));
        } catch (Exception $e) {
            Log::error('Error showing contact: ' . $e->getMessage());
            return redirect()->route('contact.index')->with('error', 'Contact not found.');
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $contact = Contact::where('contacts_id', $id)->firstOrFail();
            $contact->delete();

            return response()->json(['success' => 'Contact deleted successfully!']);
        } catch (Exception $e) {
            Log::error('Contact delete error: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to delete contact. Please try again.'], 500);
        }
    }

    /**
     * Toggle contact status
     */
    public function status(string $id)
    {
        try {
            $contact = Contact::where('contacts_id', $id)->firstOrFail();
            $contact->status = !$contact->status;
            $contact->save();

            return response()->json([
                'status' => true,
                'newStatus' => $contact->status ? 'Viewed' : 'Pending',
                'success' => 'Contact status updated successfully!'
            ]);
        } catch (Exception $e) {
            Log::error('Contact status error: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'error' => 'Failed to update contact status. Please try again.'
            ], 500);
        }
    }
}
