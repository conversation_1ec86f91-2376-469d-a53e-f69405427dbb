<?php

namespace App\Http\Controllers\Admin;

use Exception;
use App\Models\Blog;
use Illuminate\Support\Str;
use App\Helpers\FileHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Yajra\DataTables\Facades\DataTables;

class BlogController extends Controller
{
    public function index(Request $request)
    {
        try {
            if ($request->ajax()) {
                $blogs = Blog::select('blogs_id', 'name', 'slug', 'feature_image', 'status', 'published_at')
                    ->orderByDesc('updated_at')
                    ->get();

                return DataTables::of($blogs)
                    ->addIndexColumn()
                    ->editColumn('feature_image', function ($blog) {
                        $img = $blog->feature_image ? asset($blog->feature_image) : asset('assets/images/default.jpg');

                        return '<img src="' . e($img) . '" alt="Image" width="50" height="50" class="rounded">';
                    })
                    ->editColumn('status', function ($blog) {
                        $status = $blog->status ? 'Active' : 'Inactive';
                        $btnClass = $blog->status ? 'btn-success' : 'btn-secondary';
                        return '<button type="button" class="m-0 bdr btn btn-sm toggle-status ' . $btnClass . '" data-id="' . e($blog->blogs_id) . '">' . $status . '</button>';
                    })
                    ->editColumn('published_at', fn($blog) => optional($blog->published_at)->format('d M, Y'))
                    ->addColumn('actions', function ($blog) {
                        $id = e($blog->blogs_id);
                        $editUrl = route('blog.edit', $id);
                        $showUrl = route('blog.show', $id);

                        return '
                        <div class="d-flex gap-2">
                            <a href="' . $showUrl . '" class="text-info ttt" title="View"><i class="fas fa-eye"></i></a>
                            <a href="' . $editUrl . '" class="text-primary ttt" title="Edit"><i class="fas fa-edit"></i></a>
                            <button type="button" class="text-danger border-0 bg-transparent p-0 m-0 delete-button ttt" data-id="' . $id . '" title="Delete">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>';
                    })
                    ->rawColumns(['feature_image', 'status', 'actions'])
                    ->make(true);
            }

            return view('admin.blog.index');
        } catch (Exception $e) {
            Log::error('Error fetching blog data: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Something went wrong while loading the blog data.');
        }
    }

    public function create()
    {
        return view('admin.blog.create');
    }

    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'name'          => 'required|string|max:100|min:60',
                'slug'          => 'nullable|string|max:255|unique:blogs,slug',
                'feature_image' => 'required|image|mimes:jpg,jpeg,png,webp|max:5120',
                'image'         => 'required|image|mimes:jpg,jpeg,png,webp|max:5120',
                'description'   => 'required|string',
                'author_name'   => 'required|string|max:255',
                'author_image'  => 'required|image|mimes:jpg,jpeg,png,webp|max:5120',
                'status'        => 'required|in:0,1',
                'published_at'  => 'required|date',
            ]);

            if ($validator->fails()) {
                return redirect()->back()->withErrors($validator)->withInput();
            }

            $validated = $validator->validated();

            $blog = new Blog();
            $blog->name         = $validated['name'];
            $blog->slug         = Str::slug($validated['slug'] ?? $validated['name']);
            $blog->description  = $validated['description'];
            $blog->author_name  = $validated['author_name'];
            $blog->status       = $validated['status'];
            $blog->published_at = $validated['published_at'] ?? now();

            // Upload images
            if ($request->hasFile('feature_image')) {
                $blog->feature_image = FileHelper::uploadFiles($request->file('feature_image'), 'blogs', 'public');
                Log::info('Feature image uploaded.', ['path' => $blog->feature_image]);
            }

            if ($request->hasFile('image')) {
                $blog->image = FileHelper::uploadFiles($request->file('image'), 'blogs', 'public');
                Log::info('Additional image uploaded.', ['path' => $blog->image]);
            }

            if ($request->hasFile('author_image')) {
                $blog->author_image = FileHelper::uploadFiles($request->file('author_image'), 'blogs/authors', 'public');
                Log::info('Author image uploaded.', ['path' => $blog->author_image]);
            }

            $blog->save();

            return redirect()->route('blog.index')->with('success', 'Blog created successfully.');
        } catch (Exception $e) {
            Log::error('Failed to store blog.', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return redirect()->back()->with('error', 'Something went wrong. Please try again.');
        }
    }

    public function show(string $id)
    {
        $blog = Blog::where('blogs_id', $id)->firstOrFail();
        return view('admin.blog.show', compact('blog'));
    }

    public function edit(string $id)
    {
        $blog = Blog::where('blogs_id', $id)->firstOrFail();
        return view('admin.blog.edit', compact('blog'));
    }

    public function update(Request $request, string $id)
    {
        try {
            $blog = Blog::where('blogs_id', $id)->firstOrFail();

            $validator = Validator::make($request->all(), [
                'name'          => 'required|string|max:100|min:60',
                'slug'          => 'nullable|string|max:255|unique:blogs,slug,' . $id . ',blogs_id',
                'feature_image' => 'nullable|image|mimes:jpg,jpeg,png,webp|max:5120',
                'image'         => 'nullable|image|mimes:jpg,jpeg,png,webp|max:5120',
                'description'   => 'required|string',
                'author_name'   => 'required|string|max:255',
                'author_image'  => 'nullable|image|mimes:jpg,jpeg,png,webp|max:5120',
                'status'        => 'required|in:0,1',
                'published_at'  => 'required|date',
            ]);

            if ($validator->fails()) {
                return redirect()->back()->withErrors($validator)->withInput();
            }

            $validated = $validator->validated();

            $slug = Str::slug($validated['slug'] ?? $validated['name']);

            // Ensure new slug is unique
            if ($slug !== $blog->slug && Blog::where('slug', $slug)->where('blogs_id', '!=', $id)->exists()) {
                return redirect()->back()->with('error', 'The slug has already been taken.')->withInput();
            }

            // Update basic fields
            $blog->fill([
                'name'         => $validated['name'],
                'slug'         => $slug,
                'description'  => $validated['description'],
                'author_name'  => $validated['author_name'],
                'status'       => $validated['status'],
                'published_at' => $validated['published_at'],
            ]);

            // Replace feature_image
            if ($request->hasFile('feature_image')) {
                if ($blog->feature_image && Storage::disk('public')->exists($blog->feature_image)) {
                    Storage::disk('public')->delete($blog->feature_image);
                }
                $blog->feature_image = FileHelper::uploadFiles($request->file('feature_image'), 'blogs', 'public');
            }

            // Replace additional image
            if ($request->hasFile('image')) {
                if ($blog->image && Storage::disk('public')->exists($blog->image)) {
                    Storage::disk('public')->delete($blog->image);
                }
                $blog->image = FileHelper::uploadFiles($request->file('image'), 'blogs', 'public');
            }

            // Replace author image
            if ($request->hasFile('author_image')) {
                if ($blog->author_image && Storage::disk('public')->exists($blog->author_image)) {
                    Storage::disk('public')->delete($blog->author_image);
                }
                $blog->author_image = FileHelper::uploadFiles($request->file('author_image'), 'blogs/authors', 'public');
            }

            $blog->save();

            return redirect()->route('blog.index')->with('success', 'Blog updated successfully.');
        } catch (Exception $e) {
            Log::error('Failed to update blog', ['error' => $e->getMessage()]);
            return redirect()->back()->with('error', 'Something went wrong. Please try again.')->withInput();
        }
    }

    public function destroy($id)
    {
        try {
            $blog = Blog::where('blogs_id', $id)->firstOrFail();

            foreach (['feature_image', 'image', 'author_image'] as $field) {
                if ($blog->$field && Storage::disk('public')->exists($blog->$field)) {
                    Storage::disk('public')->delete($blog->$field);
                }
            }

            $blog->delete();

            return response()->json(['status' => true, 'message' => 'Blog deleted successfully.']);
        } catch (Exception $e) {
            Log::error('Failed to delete blog', ['error' => $e->getMessage()]);
            return response()->json(['status' => false, 'message' => 'Something went wrong.'], 500);
        }
    }

    public function status($id)
    {
        try {
            $blog = Blog::where('blogs_id', $id)->firstOrFail();
            $blog->status = !$blog->status;
            $blog->save();

            return response()->json([
                'status' => true,
                'newStatus' => $blog->status ? 'Active' : 'Inactive'
            ]);
        } catch (Exception $e) {
            Log::error('Failed to toggle blog status: ' . $e->getMessage());
            return response()->json(['status' => false], 500);
        }
    }

    public function upload(Request $request)
    {
        if ($request->hasFile('upload')) {
            $file = $request->file('upload');
            $filename = time() . '_' . $file->getClientOriginalName();
            $path = $file->storeAs('uploads', $filename, 'public');
            return response()->json(['url' => asset('storage/' . $path)]);
        }
        return response()->json(['url' => null], 400);
    }
}
