<?php

namespace App\Http\Controllers\Admin;

use Exception;
use App\Models\Foundation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Yajra\DataTables\Facades\DataTables;

class FoundationController extends Controller
{
    public function index(Request $request)
    {
        try {
            if ($request->ajax()) {
                $foundations = Foundation::select('foundations_id', 'name', 'email', 'mobile', 'description', 'created_at');

                return DataTables::of($foundations)
                    ->addIndexColumn()
                    ->editColumn('name', fn($f) => ucwords(strtolower($f->name ?? '')))
                    ->editColumn('email', fn($f) => strtolower($f->email ?? ''))
                    ->editColumn('mobile', fn($f) => $f->mobile ?? '')
                    ->editColumn('created_at', fn($f) => optional($f->created_at)->format('d M, Y h:i A'))

                    ->addColumn('actions', function ($f) {
                        $id = $f->foundations_id;
                        $viewRoute = route('foundation.show', $id);

                        return '
                        <div class="d-flex gap-2">
                            <a href="' . $viewRoute . '" class="text-info ttt">
                                <i class="fa-solid fa-eye" title="View"></i>
                            </a>
                            <button type="button" class="btn btn-link text-danger p-0 m-0 delete-button ttt" data-id="' . $id . '">
                                <i class="fa-solid fa-trash" title="Delete"></i>
                            </button>
                        </div>';
                    })

                    ->rawColumns(['actions'])
                    ->make(true);
            }

            return view('admin.donation.index');
        } catch (Exception $e) {
            Log::error('Error fetching foundation data: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Something went wrong while loading the data.');
        }
    }

    public function show($id)
    {
        $foundation = Foundation::where('foundations_id', $id)->firstOrFail();
        return view('admin.donation.show', compact('foundation'));
    }

    public function destroy($id)
    {
        try {
            $foundation = Foundation::where('foundations_id', $id)->firstOrFail();
            $foundation->delete();

            return response()->json(['success' => true]);
        } catch (Exception $e) {
            Log::error('Delete Error: ' . $e->getMessage());
            return response()->json(['success' => false], 500);
        }
    }
}
