<?php

namespace App\Http\Controllers\Admin;

use Exception;
use App\Models\Charity;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Yajra\DataTables\Facades\DataTables;

class CharityController extends Controller
{
    public function index(Request $request)
    {
        try {
            if ($request->ajax()) {

                $charities = Charity::select(DB::raw('
                    MAX(charities_id) as charities_id,
                    full_name,
                    email,
                    mobile_no,
                    MAX(amount) as amount,
                    MAX(created_at) as created_at
                '))
                    ->groupBy('email', 'full_name', 'mobile_no');

                return DataTables::of($charities)
                    ->addIndexColumn()
                    ->editColumn('full_name', fn($c) => ucwords(strtolower($c->full_name ?? '')))
                    ->editColumn('email', fn($c) => strtolower($c->email ?? ''))
                    ->editColumn('mobile_no', fn($c) => $c->mobile_no ?? '-')
                    ->editColumn('amount', fn($c) => '₹' . number_format($c->amount, 2))
                    ->editColumn('created_at', fn($c) => optional($c->created_at)->format('d M, Y h:i A'))

                    ->addColumn('actions', function ($c) {
                        $id = $c->charities_id;
                        $viewRoute = route('charity.show', $id);

                        return '
                        <a href="' . $viewRoute . '" class="text-info ttt">
                            <i class="fa-solid fa-eye" title="View"></i>
                        </a>';
                    })

                    ->rawColumns(['actions'])
                    ->make(true);
            }

            return view('admin.charity.index');
        } catch (Exception $e) {
            Log::error('Error fetching charity data: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Something went wrong while loading the data.');
        }
    }

    public function show($id)
    {
        $charity = Charity::with('payments')->where('charities_id', $id)->firstOrFail();
        return view('admin.charity.show', compact('charity'));
    }
}
